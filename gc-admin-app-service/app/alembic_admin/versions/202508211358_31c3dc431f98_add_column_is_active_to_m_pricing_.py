"""add column is_active to m_pricing_storages

Revision ID: 31c3dc431f98
Revises: 9d605aba0a5c
Create Date: 2025-08-21 13:58:56.984501

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "31c3dc431f98"  # pragma: allowlist secret
down_revision: Union[str, None] = "9d605aba0a5c"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "m_pricing_storages", sa.Column("is_active", sa.<PERSON>an(), nullable=False)
    )
    op.alter_column(
        "m_pricing_storages",
        "storage",
        existing_type=sa.INTEGER(),
        comment="",
        existing_nullable=False,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "m_pricing_storages",
        "storage",
        existing_type=sa.INTEGER(),
        comment=None,
        existing_comment="",
        existing_nullable=False,
    )
    op.drop_column("m_pricing_storages", "is_active")
    # ### end Alembic commands ###
