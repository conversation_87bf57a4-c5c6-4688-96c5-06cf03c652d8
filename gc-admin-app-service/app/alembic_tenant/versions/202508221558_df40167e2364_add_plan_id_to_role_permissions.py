"""add plan_id to role_permissions

Revision ID: df40167e2364
Revises: a89851beccdd
Create Date: 2025-08-22 15:58:57.999359

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "df40167e2364"
down_revision: Union[str, None] = "a89851beccdd"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("role_permissions", sa.Column("plan_id", sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("role_permissions", "plan_id")
    # ### end Alembic commands ###
