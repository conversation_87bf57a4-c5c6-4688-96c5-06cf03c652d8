from datetime import datetime, timedelta, timezone

from configuration.context.tenant_context import (
    reset_current_db_name,
    set_current_db_name,
)
from core.messages import CustomMessageCode
from db.db_connection import TenantDatabase
from enums.pricing_enum import PricingStoragePeriod
from fastapi_pagination import Page, Params
from fastapi_pagination.ext.sqlalchemy import paginate
from schemas.requests.pricing import ExtraStorageRequestSchema
from schemas.responses.pricing import TenantExtraStorageResponse
from sqlalchemy import select
from sqlalchemy.exc import DBAPIError, OperationalError
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.central_models import (
    MasterPricingStorage,
    TenantClinic,
    TenantExtraStorage,
)
from gc_dentist_shared.core.common.redis import RedisCli
from gc_dentist_shared.core.constants import StorageRedis
from gc_dentist_shared.core.decorators.retry import retry_on_failure
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.tenant_models import TenantConfiguration


class PricingService:
    def __init__(self, central_db_session: AsyncSession):
        self.central_db_session = central_db_session

    # region Public Methods
    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="create_extra_storage",
    )
    async def create_extra_storage(self, data: ExtraStorageRequestSchema, configuration):
        log.info("🚀 Start create extra storage")

        async with self.central_db_session.begin():
            pricing_storage = await self._get_pricing_storages(
                data.pricing_storage_id, self.central_db_session
            )
            if not pricing_storage:
                raise CustomValueError(
                    message=CustomMessageCode.PRICING_STORAGE_NOT_FOUND.title,
                    message_code=CustomMessageCode.PRICING_STORAGE_NOT_FOUND.code,
                )

            tenant = await self._get_tenant_uuid(
                data.tenant_uuid, self.central_db_session
            )
            if not tenant:
                raise CustomValueError(
                    message=CustomMessageCode.TENANT_NOT_FOUND.title,
                    message_code=CustomMessageCode.TENANT_NOT_FOUND.code,
                )

            extra_storage = await self._create_extra_storage(
                tenant, pricing_storage, self.central_db_session
            )
            log.info("✅ Create pricing extra storage successfully in central database")

            # Sync extra storage data to tenant_configuration(TenantDB)
            await self._sync_extra_storage_to_tenant(
                tenant.db_name, tenant.tenant_uuid, pricing_storage.storage, configuration
            )

        log.info("✅ Create pricing extra storage successfully")
        return extra_storage

    async def get_list_pricing_extra_storage(
        self, tenant_uuid: str, params: Params = None, search: str | None = None
    ) -> Page[TenantExtraStorageResponse]:
        query = await self._build_list_tenant_extra_storage(
            tenant_uuid=tenant_uuid, search=search
        )
        return await paginate(
            self.central_db_session, query, params=params, unique=False
        )

    # endregion

    # region Private Methods
    async def _get_pricing_storages(
        self, storage_id: int, db_session: AsyncSession
    ) -> MasterPricingStorage:
        query = select(MasterPricingStorage).where(
            MasterPricingStorage.id == storage_id,
            MasterPricingStorage.is_active.is_(True),
        )
        result = await db_session.execute(query)
        return result.scalar_one_or_none()

    async def _get_tenant_uuid(
        self, tenant_uuid: str, db_session: AsyncSession
    ) -> TenantClinic:
        query = select(TenantClinic).where(TenantClinic.tenant_uuid == tenant_uuid)
        result = await db_session.execute(query)
        return result.scalar_one_or_none()

    async def _create_extra_storage(
        self, tenant, storage_plan, db_session: AsyncSession
    ):
        period_id = storage_plan.period
        days = PricingStoragePeriod.get_days_by_period_id(period_id)
        if days is None:
            raise CustomValueError(
                message=CustomMessageCode.PRICING_EXTRA_STORAGE_PERIOD_NOT_FOUND.title,
                message_code=CustomMessageCode.PRICING_EXTRA_STORAGE_PERIOD_NOT_FOUND.code,
            )

        expired_at = (
            None
            if days == 0
            else (datetime.now(timezone.utc) + timedelta(days=days)).replace(
                tzinfo=None
            )
        )

        data = TenantExtraStorage(
            name=storage_plan.name,
            tenant_uuid=tenant.tenant_uuid,
            storage_id=storage_plan.id,
            expired_at=expired_at,
        )
        db_session.add(data)
        await db_session.flush()
        await db_session.refresh(data)
        return data

    async def _build_list_tenant_extra_storage(
        self, tenant_uuid: str, search: str | None = None
    ):
        fields = [
            TenantExtraStorage.id,
            TenantExtraStorage.name,
            TenantExtraStorage.storage_id,
            TenantExtraStorage.expired_at,
            TenantExtraStorage.created_at,
            TenantExtraStorage.updated_at,
        ]

        where_clause = [TenantExtraStorage.tenant_uuid == tenant_uuid]

        if search:
            search_pattern = f"%{search}%"
            where_clause.append(TenantExtraStorage.name.ilike(search_pattern))

        query = (
            select(*fields)
            .where(*where_clause)
            .order_by(TenantExtraStorage.created_at.desc())
        )
        return query

    async def _sync_extra_storage_to_tenant(
        self, tenant_db_name: str, tenant_uuid: str, storage_amount: int, configuration
    ):
        token = set_current_db_name(tenant_db_name)

        try:
            log.info(f"🚀 Start sync extra storage to tenant: {tenant_uuid}")
            tenant_db_session = await TenantDatabase.get_instance_tenant_db()
            async with tenant_db_session.begin():
                result = await self._update_tenant_configuration(
                    tenant_uuid, storage_amount, tenant_db_session
                )

            redis_cli = await RedisCli.get_instance(configuration)
            total_storage = result.extra_storage + result.default_storage
            await self._update_storage_limit_in_redis(
                redis_cli, tenant_uuid, storage_amount, total_storage
            )

            log.info(f"✅ Successfully synced extra storage to tenant: {tenant_uuid}")
        except Exception as e:
            log.error(f"❌ Failed to sync extra storage to tenant: {e}")
            raise e
        finally:
            reset_current_db_name(token)

    async def _update_tenant_configuration(
        self, tenant_uuid: str, storage: int, db_session: AsyncSession
    ):
        query_get = select(TenantConfiguration).where(
            TenantConfiguration.tenant_uuid == tenant_uuid
        )
        result = await db_session.execute(query_get)
        tenant_config = result.scalar_one_or_none()

        if not tenant_config:
            raise CustomValueError(
                message=CustomMessageCode.PRICING_STORAGE_TENANT_CONFIGRATION_NOT_FOUND.title,
                message_code=CustomMessageCode.PRICING_STORAGE_TENANT_CONFIGRATION_NOT_FOUND.code,
            )

        extra_storage = tenant_config.extra_storage or 0
        new_storage_total = extra_storage + storage
        tenant_config.extra_storage = new_storage_total
        await db_session.flush()
        await db_session.refresh(tenant_config)
        return tenant_config

    async def _update_storage_limit_in_redis(
            self, redis_cli, tenant_uuid: str, storage_amount: int, total_storage: int
    ):
        prefix = StorageRedis.STORAGE_LIMIT.value % tenant_uuid

        old_value = await redis_cli.get(prefix)
        if old_value is not None:
            new_value = await redis_cli.incrby(prefix, storage_amount)
            log.info(
                f"🔄 Updated Redis storage for tenant {tenant_uuid}: "
                f"{old_value} -> {new_value}"
            )
        else:
            await redis_cli.set(prefix, total_storage)
            log.info(
                f"✨ Initialized Redis storage for tenant {tenant_uuid}: {total_storage}"
            )

    # endregion
