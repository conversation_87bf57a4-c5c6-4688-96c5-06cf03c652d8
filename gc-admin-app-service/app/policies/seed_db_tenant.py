import os
import sys
from datetime import datetime

import yaml
from sqlalchemy import select, update

from gc_dentist_shared.central_models.clinic_tenants import TenantClinic
from gc_dentist_shared.central_models.m_plans import MasterPlan
from gc_dentist_shared.central_models.m_pricing_storages import MasterPricingStorage
from gc_dentist_shared.central_models.tenant_extra_storages import TenantExtraStorage
from gc_dentist_shared.central_models.tenant_plans import TenantPlan
from gc_dentist_shared.core.enums.role_enum import RoleKeyEnum
from gc_dentist_shared.tenant_models.permissions import Permission
from gc_dentist_shared.tenant_models.role_permissions import RolePermission
from gc_dentist_shared.tenant_models.roles import Roles
from gc_dentist_shared.tenant_models.tenant_config import TenantConfiguration

sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from db.db_connection import CentralDatabase  # noqa: E402
from db.db_connection import TenantDatabase  # noqa: E402


def get_role_key_from_name(role_name: str) -> int:
    """Map role name to role key using RoleKeyEnum"""
    role_mapping = {
        "Admin": RoleKeyEnum.ADMIN,
        "Doctor": RoleKeyEnum.DOCTOR,
        "Patient": RoleKeyEnum.PATIENT,
    }
    return role_mapping.get(
        role_name, RoleKeyEnum.PATIENT
    )  # Default to PATIENT if not found


def parse_permissions(tree: dict, module: str = "", path: list[str] | None = None):
    if path is None:
        path = []

    permissions = []
    for key, value in tree.items():
        current_path = [*path, key] if key else path
        if isinstance(value, list):
            full_path = ".".join(filter(None, current_path))
            for action in value:
                permission_key = (
                    f"{module}.{full_path}:{action}"
                    if full_path
                    else f"{module}:{action}"
                )
                permissions.append(
                    Permission(
                        module=module,
                        sub_module=full_path or None,
                        action=action,
                        permission_key=permission_key,
                        delete_flag=False,
                    )
                )
        elif isinstance(value, dict):
            permissions += parse_permissions(value, module, current_path)
    return permissions


async def sync_plan_permissions(tenant_db_names: list[str], plan_id: int):
    """Sync plan permissions for multiple tenants"""
    for tenant_db_name in tenant_db_names:
        try:
            print(
                f"\n🔄 Syncing plan permissions for tenant: {tenant_db_name} with plan_id: {plan_id}"
            )
            tenant_db = await TenantDatabase.get_instance_tenant_db_by_name(
                tenant_db_name
            )

            # Load plan permissions from YAML
            with open("central/plan_permission.yaml") as f:
                plan_permissions_data = yaml.safe_load(f)

            # Load existing permissions
            async with tenant_db.begin():
                execute_permission = await tenant_db.execute(
                    select(Permission.id, Permission.permission_key).where(
                        Permission.delete_flag.is_(False)
                    )
                )
                permissions = execute_permission.mappings().all()
                perm_map = {p["permission_key"]: p["id"] for p in permissions}

                print(f"📋 Found {len(permissions)} existing permissions")

                # Check if plan_id column exists by trying to query it
                try:
                    # Try to load existing role-permission links for this plan
                    result = await tenant_db.execute(
                        select(
                            RolePermission.role_id,
                            RolePermission.permission_id,
                            RolePermission.id,
                        ).where(RolePermission.plan_id == plan_id)
                    )
                    existing_links = {
                        (r["role_id"], r["permission_id"]): r["id"]
                        for r in result.mappings().all()
                    }
                    print(
                        f"📋 Found {len(existing_links)} existing plan-permission links for plan_id: {plan_id}"
                    )
                except Exception as e:
                    if "plan_id" in str(e) and "does not exist" in str(e):
                        print(
                            f"⚠️ Column 'plan_id' does not exist in role_permissions table yet. Skipping plan permissions sync."
                        )
                        print(f"💡 Please run migration to add plan_id column first.")
                        return
                    else:
                        raise e

                # Function to match permission keys based on patterns
                def match_permission_keys(pattern, all_keys):
                    if pattern == "*":
                        return list(all_keys.keys())
                    elif pattern.endswith(":*"):
                        prefix = pattern[:-2]
                        return [k for k in all_keys if k.startswith(f"{prefix}")]
                    elif ":" in pattern:
                        module, action = pattern.split(":", 1)
                        return [
                            k
                            for k in all_keys
                            if k.endswith(f":{action}")
                            and k.startswith(f"{module}.")
                            or k == pattern
                        ]
                    return [pattern]

                # Get all available permission keys from permissions.yaml
                with open("tenant/permissions.yaml") as f:
                    permissions_data = yaml.safe_load(f)

                all_available_keys = set()
                for module, sub_tree in permissions_data.items():
                    if sub_tree:
                        parsed = parse_permissions(sub_tree, module=module)
                        for p in parsed:
                            all_available_keys.add(p.permission_key)

                print(f"📋 Available permissions from YAML: {len(all_available_keys)}")

                # Process each plan type (Basic, Premium, etc.)
                for plan_type, patterns in plan_permissions_data.items():
                    if not patterns:
                        print(f"[WARN] No patterns found for plan type: {plan_type}")
                        continue

                    print(
                        f"🔍 Processing plan type: {plan_type} with {len(patterns)} patterns"
                    )

                    # Determine which permissions to create for this plan type
                    permissions_to_create = set()
                    for pattern in patterns:
                        if pattern == "*":
                            # If * exists, create all permissions
                            permissions_to_create.update(all_available_keys)
                            print(
                                f"🔍 Pattern '{pattern}' matches all {len(all_available_keys)} permissions"
                            )
                            break
                        else:
                            # Add permissions that match the pattern
                            matched = match_permission_keys(
                                pattern, {k: True for k in all_available_keys}
                            )
                            permissions_to_create.update(matched)
                            print(
                                f"🔍 Pattern '{pattern}' matches {len(matched)} permissions"
                            )

                    print(
                        f"📋 Total permissions to create for plan type {plan_type}: {len(permissions_to_create)}"
                    )

                    # Create plan-permission mappings
                    for permission_key in permissions_to_create:
                        perm_id = perm_map.get(permission_key)
                        if not perm_id:
                            print(
                                f"[WARN] Permission not found for key: {permission_key}"
                            )
                            continue

                        # Create role_permission record with plan_id (no role_id for plan-based permissions)
                        pair_key = (
                            None,
                            perm_id,
                        )  # role_id is None for plan-based permissions
                        if pair_key in existing_links:
                            # Update existing record
                            await tenant_db.execute(
                                update(RolePermission)
                                .where(RolePermission.id == existing_links[pair_key])
                                .values(plan_id=plan_id, delete_flag=False)
                            )
                        else:
                            # Create new record
                            tenant_db.add(
                                RolePermission(
                                    role_id=None,  # No specific role for plan-based permissions
                                    permission_id=perm_id,
                                    plan_id=plan_id,
                                    delete_flag=False,
                                )
                            )

            print(
                f"✅ Plan permissions synced successfully for {tenant_db_name} with plan_id: {plan_id}"
            )
        except Exception as e:
            print(f"❌ Failed to sync plan permissions for {tenant_db_name}: {str(e)}")
            raise


async def sync_roles_permissions(tenant_db_names: list[str]):
    """Sync roles and permissions for multiple tenants"""
    for tenant_db_name in tenant_db_names:
        try:
            print(f"\n🔄 Syncing roles and permissions for tenant: {tenant_db_name}")
            tenant_db = await TenantDatabase.get_instance_tenant_db_by_name(
                tenant_db_name
            )

            # Load role permissions from YAML
            with open("tenant/role_permissions.yaml") as f:
                roles_data = yaml.safe_load(f)

            # Mark all role-permission links as deleted
            async with tenant_db.begin():
                await tenant_db.execute(update(RolePermission).values(delete_flag=True))

            # Load role and permission mappings
            async with tenant_db.begin():
                execute_roles = await tenant_db.execute(
                    select(Roles.id, Roles.role_key)
                )
                roles = execute_roles.mappings().all()
                role_map = {r["role_key"]: r["id"] for r in roles}

                # Load existing permissions
                execute_permission = await tenant_db.execute(
                    select(Permission.id, Permission.permission_key).where(
                        Permission.delete_flag.is_(False)
                    )
                )
                permissions = execute_permission.mappings().all()
                perm_map = {p["permission_key"]: p["id"] for p in permissions}

                print(
                    f"📋 Found {len(roles)} roles and {len(permissions)} existing permissions"
                )

                # Load existing role-permission links
                result = await tenant_db.execute(
                    select(
                        RolePermission.role_id,
                        RolePermission.permission_id,
                        RolePermission.id,
                    )
                )
                existing_links = {
                    (r["role_id"], r["permission_id"]): r["id"]
                    for r in result.mappings().all()
                }

                print(
                    "📋 Found {} existing role-permission links".format(
                        len(existing_links)
                    )
                )

                # Collect all permission patterns from roles
                all_permission_patterns = set()
                for _, patterns in roles_data.items():
                    if patterns:
                        all_permission_patterns.update(patterns)

                print(
                    f"📋 Permission patterns from roles: {len(all_permission_patterns)}"
                )

                # Function to match permission keys based on patterns
                def match_permission_keys(pattern, all_keys):
                    if pattern == "*":
                        return list(all_keys.keys())
                    elif pattern.endswith(":*"):
                        prefix = pattern[:-2]
                        return [k for k in all_keys if k.startswith(f"{prefix}")]
                    elif ":" in pattern:
                        module, action = pattern.split(":", 1)
                        return [
                            k
                            for k in all_keys
                            if k.endswith(f":{action}")
                            and k.startswith(f"{module}.")
                            or k == pattern
                        ]
                    return [pattern]

                # Get all available permission keys from permissions.yaml
                with open("tenant/permissions.yaml") as f:
                    permissions_data = yaml.safe_load(f)

                all_available_keys = set()
                for module, sub_tree in permissions_data.items():
                    if sub_tree:
                        parsed = parse_permissions(sub_tree, module=module)
                        for p in parsed:
                            all_available_keys.add(p.permission_key)

                print(f"📋 Available permissions from YAML: {len(all_available_keys)}")

                # Determine which permissions to actually create based on role patterns
                permissions_to_create = set()
                for pattern in all_permission_patterns:
                    if pattern == "*":
                        # If * exists, create all permissions
                        permissions_to_create.update(all_available_keys)
                        print(
                            f"🔍 Pattern '{pattern}' matches all {len(all_available_keys)} permissions"
                        )
                        break
                    else:
                        # Add permissions that match the pattern
                        matched = match_permission_keys(
                            pattern, {k: True for k in all_available_keys}
                        )
                        permissions_to_create.update(matched)
                        print(
                            f"🔍 Pattern '{pattern}' matches {len(matched)} permissions"
                        )

                print(f"📋 Total permissions to create: {len(permissions_to_create)}")

                # Create missing permissions
                perm_objs = []
                for permission_key in permissions_to_create:
                    if permission_key not in perm_map:
                        # Find the permission data and create new one
                        for module, sub_tree in permissions_data.items():
                            if sub_tree:
                                parsed = parse_permissions(sub_tree, module=module)
                                for p in parsed:
                                    if p.permission_key == permission_key:
                                        perm_objs.append(p)
                                        break

                if perm_objs:
                    tenant_db.add_all(perm_objs)

            # Create permissions in separate transaction
            if perm_objs:
                async with tenant_db.begin():
                    tenant_db.add_all(perm_objs)
                print(f"📋 Created {len(perm_objs)} new permissions")

            # Now create role-permission mappings in a new transaction
            async with tenant_db.begin():
                # Reload permissions after creating new ones
                execute_permission = await tenant_db.execute(
                    select(Permission.id, Permission.permission_key).where(
                        Permission.delete_flag.is_(False)
                    )
                )
                permissions = execute_permission.mappings().all()
                perm_map = {p["permission_key"]: p["id"] for p in permissions}

                print(f"📋 Total permissions after creation: {len(permissions)}")

                # Now create role-permission mappings
                for role_key, patterns in roles_data.items():
                    if not patterns:
                        print(f"[WARN] Pattern not found for role key: {role_key}")
                        continue

                    # Map role name to role key using the same function
                    try:
                        role_key_int = get_role_key_from_name(role_key)
                    except (ValueError, KeyError):
                        print(f"[WARN] Invalid role key: {role_key}")
                        continue

                    role_id = role_map.get(role_key_int)
                    if not role_id:
                        print(f"[WARN] Role not found for role key: {role_key_int}")
                        continue

                    print(
                        f"🔍 Processing role: {role_key} (ID: {role_id}) with {len(patterns)} patterns"
                    )

                    matched_keys = set()
                    for pattern in patterns:
                        matched_keys.update(match_permission_keys(pattern, perm_map))

                    print(f"🔍 Role {role_key} matched {len(matched_keys)} permissions")

                    for key in matched_keys:
                        perm_id = perm_map.get(key)
                        if not perm_id:
                            print(f"[WARN] Permission not found for key: {key}")
                            continue

                        pair_key = (role_id, perm_id)
                        if pair_key in existing_links:
                            await tenant_db.execute(
                                update(RolePermission)
                                .where(RolePermission.id == existing_links[pair_key])
                                .values(delete_flag=False)
                            )
                        else:
                            tenant_db.add(
                                RolePermission(
                                    role_id=role_id,
                                    permission_id=perm_id,
                                    delete_flag=False,
                                )
                            )

            print(f"✅ Roles and permissions mapped successfully for {tenant_db_name}.")
        except Exception as e:
            print(f"❌ Failed to seed roles_permissions for {tenant_db_name}: {str(e)}")
            raise


async def sync_roles(tenant_db_names: list[str]):
    """Sync roles for multiple tenants"""
    # Get the directory where this script is located
    for tenant_db_name in tenant_db_names:
        try:
            print(f"\n🔄 Syncing roles for tenant: {tenant_db_name}")
            tenant_db = await TenantDatabase.get_instance_tenant_db_by_name(
                tenant_db_name
            )
            async with tenant_db.begin():
                with open("tenant/roles.yaml") as f:
                    roles_data = yaml.safe_load(f)

                # Mark all roles as deleted
                await tenant_db.execute(
                    update(Roles).values(delete_flag=True, is_system=True)
                )

                # Load existing roles
                result = await tenant_db.execute(select(Roles.id, Roles.role_key))
                existing_roles = {
                    r["role_key"]: r["id"] for r in result.mappings().all()
                }

                # Load roles from YAML and create/update them
                roles = roles_data.get("roles", [])
                for role in roles:
                    # Extract role_name from role data and map to role_key
                    role_name = role["role_name"]
                    role_key = get_role_key_from_name(role_name)

                    if role_key in existing_roles:
                        await tenant_db.execute(
                            update(Roles)
                            .where(Roles.id == existing_roles[role_key])
                            .values(delete_flag=False)
                        )
                    else:
                        role_obj = Roles(
                            role_key=role_key,  # Use mapped role_key
                            is_active=role["is_active"],
                            is_system=role["is_system"],
                            delete_flag=False,
                        )
                        tenant_db.add(role_obj)
                await tenant_db.commit()
                print(f"✅ Roles seeded successfully for {tenant_db_name}.")
        except Exception as e:
            print(f"❌ Failed to seed roles for {tenant_db_name}: {str(e)}")
            raise


async def sync_plan_storage(
    tenant_db_names: list[str], plan_id: int, pricing_storage_id: int = None
):
    """Sync plan storage and create tenant plan records for multiple tenants"""
    for tenant_db_name in tenant_db_names:
        try:
            print(f"\n🔄 Syncing plan and storage for tenant: {tenant_db_name}")
            # Get tenant database for tenant-specific operations
            tenant_db = await TenantDatabase.get_instance_tenant_db_by_name(
                tenant_db_name
            )

            # Get central database for master_* and tenant_* tables
            central_db = await CentralDatabase.get_instance_db()

            # First, validate plan_id exists in m_plans (central database)
            async with central_db.begin():
                plan_result = await central_db.execute(
                    select(
                        MasterPlan.id, MasterPlan.code, MasterPlan.default_storage
                    ).where(MasterPlan.id == plan_id)
                )
                plan_record = plan_result.mappings().first()

                if not plan_record:
                    print(f"⚠️ Plan not found for plan_id: {plan_id}")
                    continue

                plan_code = plan_record["code"]
                default_storage = plan_record["default_storage"]
                print(
                    "Found plan: {} for plan_id: {} with default_storage: {}".format(
                        plan_code, plan_id, default_storage
                    )
                )

                # Validate pricing_storage_id if provided and get storage amount (central database)
                extra_storage = 0
                if pricing_storage_id:
                    pricing_storage_result = await central_db.execute(
                        select(
                            MasterPricingStorage.id,
                            MasterPricingStorage.name,
                            MasterPricingStorage.storage,
                        ).where(MasterPricingStorage.id == pricing_storage_id)
                    )
                    pricing_storage_record = pricing_storage_result.mappings().first()

                    if not pricing_storage_record:
                        print(
                            "⚠️ Pricing storage not found for pricing_storage_id: {}".format(
                                pricing_storage_id
                            )
                        )
                        continue

                    pricing_storage_name = pricing_storage_record["name"]
                    extra_storage = pricing_storage_record["storage"]
                    print(
                        "Found pricing storage: {} for pricing_storage_id: {} with storage: {}".format(
                            pricing_storage_name, pricing_storage_id, extra_storage
                        )
                    )

                # Get tenant_uuid from tenant_clinics table (central database)
                tenant_clinic_result = await central_db.execute(
                    select(TenantClinic.tenant_uuid).where(
                        TenantClinic.db_name == tenant_db_name
                    )
                )
                tenant_uuid = tenant_clinic_result.scalar_one_or_none()

                if not tenant_uuid:
                    print(f"⚠️ Tenant clinic not found for db_name: {tenant_db_name}")
                    continue

                print(f"Found tenant_uuid: {tenant_uuid} for db_name: {tenant_db_name}")

                # Create/update tenant_plans record (central database)
                existing_tenant_plan = await central_db.execute(
                    select(TenantPlan.id).where(TenantPlan.tenant_uuid == tenant_uuid)
                )
                tenant_plan_record = existing_tenant_plan.scalar_one_or_none()

                if tenant_plan_record:
                    # Update existing tenant plan
                    await central_db.execute(
                        update(TenantPlan)
                        .where(TenantPlan.id == tenant_plan_record)
                        .values(plan_id=plan_id)
                    )
                    print(
                        "✅ Updated tenant plan for {} with plan_id: {}".format(
                            tenant_db_name, plan_id
                        )
                    )
                else:
                    # Create new tenant plan
                    new_tenant_plan = TenantPlan(
                        tenant_uuid=tenant_uuid,
                        plan_id=plan_id,
                        start_at=datetime.now(),
                    )
                    central_db.add(new_tenant_plan)
                    print(
                        "✅ Created tenant plan for {} with plan_id: {}".format(
                            tenant_db_name, plan_id
                        )
                    )

                # Create tenant_extra_storage record if pricing_storage_id is provided (central database)
                if pricing_storage_id:
                    # Check if tenant_extra_storage already exists
                    existing_storage = await central_db.execute(
                        select(TenantExtraStorage.id).where(
                            TenantExtraStorage.tenant_uuid == tenant_uuid
                        )
                    )
                    storage_record = existing_storage.scalar_one_or_none()

                    if storage_record:
                        # Update existing storage record
                        await central_db.execute(
                            update(TenantExtraStorage)
                            .where(TenantExtraStorage.id == storage_record)
                            .values(
                                storage_id=pricing_storage_id,
                                name=f"Extra storage for {tenant_db_name}",
                            )
                        )
                        print(
                            f"✅ Updated tenant extra storage for {tenant_db_name} with pricing_storage_id: {pricing_storage_id}"
                        )
                    else:
                        # Create new storage record
                        new_storage = TenantExtraStorage(
                            tenant_uuid=tenant_uuid,
                            storage_id=pricing_storage_id,
                            name="Extra storage for {}".format(tenant_db_name),
                        )
                        central_db.add(new_storage)
                        print(
                            f"✅ Created tenant extra storage for {tenant_db_name} with pricing_storage_id: {pricing_storage_id}"
                        )

                await central_db.commit()
                print(
                    f"✅ Central database operations completed for tenant: {tenant_db_name}"
                )

            # Now update tenant_configuration in tenant database
            async with tenant_db.begin():
                # Upsert tenant_configuration with plan_id, default_storage, and extra_storage
                existing_config = await tenant_db.execute(
                    select(TenantConfiguration.tenant_uuid).where(
                        TenantConfiguration.tenant_name == tenant_db_name
                    )
                )
                config_record = existing_config.scalar_one_or_none()

                if config_record:
                    # Update existing tenant configuration
                    await tenant_db.execute(
                        update(TenantConfiguration)
                        .where(TenantConfiguration.tenant_uuid == config_record)
                        .values(
                            plan_id=plan_id,
                            default_storage=default_storage,
                            extra_storage=extra_storage,
                        )
                    )
                    print(
                        "✅ Updated tenant configuration for {} with plan_id: {}, "
                        "default_storage: {}, extra_storage: {}".format(
                            tenant_db_name, plan_id, default_storage, extra_storage
                        )
                    )
                else:
                    # Create new tenant configuration
                    new_config = TenantConfiguration(
                        tenant_name=tenant_db_name,
                        tenant_slug=tenant_db_name,  # Using tenant_name as slug for now
                        plan_id=plan_id,
                        default_storage=default_storage,
                        extra_storage=extra_storage,
                    )
                    tenant_db.add(new_config)
                    print(
                        "✅ Created tenant configuration for {} with plan_id: {}, "
                        "default_storage: {}, extra_storage: {}".format(
                            tenant_db_name, plan_id, default_storage, extra_storage
                        )
                    )

                await tenant_db.commit()
                print(
                    f"✅ Tenant database operations completed for tenant: {tenant_db_name}"
                )

            print(
                f"✅ Plan and storage synced successfully for tenant: {tenant_db_name}"
            )

        except Exception as e:
            print(f"❌ Failed to sync plan and storage for {tenant_db_name}: {str(e)}")
            raise
