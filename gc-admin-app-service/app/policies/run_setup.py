import asyncio
import sys
from typing import Optional

from seed_db_tenant import (
    sync_plan_permissions,
    sync_plan_storage,
    sync_roles,
    sync_roles_permissions,
)


async def process_sync(
    tenant_db_names: list[str],
    plan_id: int = 1,
    pricing_storage_id: Optional[int] = None,
):
    if not tenant_db_names:
        print("No tenant databases specified, skipping all operations")
        return

    print(f"📋 Using plan_id: {plan_id}")
    if pricing_storage_id:
        print(f"📋 Using pricing_storage_id: {pricing_storage_id}")

    print(f"\n🔄 Processing tenants: {', '.join(tenant_db_names)}")
    success = True
    try:
        # Step 1: Sync roles first
        await sync_roles(tenant_db_names)
        # Step 2: Sync roles and permissions (creates permissions automatically)
        await sync_roles_permissions(tenant_db_names)
        # Step 3: Sync plan permissions (creates plan-based permissions)
        # might be skipped if plan_id column doesn't exist
        try:
            await sync_plan_permissions(tenant_db_names, plan_id)
        except Exception as e:
            if "plan_id column doesn't exist" in str(e):
                print("⚠️ Skipping plan permissions sync: plan_id column doesn't exist")
            else:
                raise e
        # Step 4: Sync plan and storage
        await sync_plan_storage(
            tenant_db_names, plan_id, pricing_storage_id
        )  # Pass both IDs
        print("✅ All tenants processed successfully")
    except Exception as e:
        print(f"❌ Failed to process tenants: {str(e)}")
        success = False

    if success:
        print("✅ All operations completed successfully")
    else:
        print("❌ Some operations failed")


# Remove if when integrate create tenant service
if __name__ == "__main__":
    # Parse command line arguments
    # Format: python run_setup.py [plan_id] [pricing_storage_id] tenant1 tenant2 ...
    if len(sys.argv) < 2:
        print(
            "Usage: python run_setup.py [plan_id] [pricing_storage_id] tenant1 tenant2 ..."
        )
        print("Example: python run_setup.py 1 2 tenant_template acd")
        sys.exit(1)

    try:
        plan_id = int(sys.argv[1])
        pricing_storage_id = (
            int(sys.argv[2]) if len(sys.argv) > 2 and sys.argv[2].isdigit() else None
        )
        tenant_db_names = (
            sys.argv[3:]
            if len(sys.argv) > 3
            else sys.argv[2:] if pricing_storage_id is None else sys.argv[3:]
        )
    except ValueError:
        print("Error: plan_id and pricing_storage_id must be integers")
        print(
            "Usage: python run_setup.py [plan_id] [pricing_storage_id] tenant1 tenant2 ..."
        )
        sys.exit(1)

    if tenant_db_names:
        print(f"📋 Processing tenants: {', '.join(tenant_db_names)}")
        asyncio.run(process_sync(tenant_db_names, plan_id, pricing_storage_id))
    else:
        print("📋 No tenants specified, skipping all operations")
