#!/usr/bin/env python3
"""
Permission Key Enums for Admin operations.
Based on the permissions.yaml structure and API usage.
"""

from enums.base import StrEnum


class PermissionRedisKey(StrEnum):
    """Redis cache keys for permission system"""

    PERMISSION_CACHE_PREFIX = "noda_data:user_role_permission"


class AdminPermissionKeyEnum(StrEnum):
    """Admin permission keys for various operations."""

    # Authentication & Authorization
    AUTH_VIEW = "auth:VIEW"
    AUTH_CREATE = "auth:CREATE"
    AUTH_UPDATE = "auth:UPDATE"
    AUTH_DELETE = "auth:DELETE"

    # S3 Authentication
    AUTH_S3_VIEW = "auth-s3:VIEW"
    AUTH_S3_CREATE = "auth-s3:CREATE"
    AUTH_S3_UPDATE = "auth-s3:UPDATE"
    AUTH_S3_DELETE = "auth-s3:DELETE"

    # Permissions Management
    PERMISSIONS_VIEW = "permissions:VIEW"
    PERMISSIONS_CREATE = "permissions:CREATE"
    PERMISSIONS_UPDATE = "permissions:UPDATE"
    PERMISSIONS_DELETE = "permissions:DELETE"

    # Clinic Management
    CLINIC_VIEW = "clinic:VIEW"
    CLINIC_CREATE = "clinic:CREATE"
    CLINIC_UPDATE = "clinic:UPDATE"
    CLINIC_DELETE = "clinic:DELETE"

    # Doctor Management
    DOCTORS_VIEW = "doctors:VIEW"
    DOCTORS_CREATE = "doctors:CREATE"
    DOCTORS_UPDATE = "doctors:UPDATE"
    DOCTORS_DELETE = "doctors:DELETE"

    # Patient Management
    PATIENTS_VIEW = "patients:VIEW"
    PATIENTS_CREATE = "patients:CREATE"
    PATIENTS_UPDATE = "patients:UPDATE"
    PATIENTS_DELETE = "patients:DELETE"

    # Patient Waiting Management
    PATIENT_WAITINGS_VIEW = "patient-waitings:VIEW"
    PATIENT_WAITINGS_CREATE = "patient-waitings:CREATE"
    PATIENT_WAITINGS_UPDATE = "patient-waitings:UPDATE"
    PATIENT_WAITINGS_DELETE = "patient-waitings:DELETE"

    # Master Data Management
    MASTER_DATA_VIEW = "master-data:VIEW"
    MASTER_DATA_CREATE = "master-data:CREATE"
    MASTER_DATA_UPDATE = "master-data:UPDATE"
    MASTER_DATA_DELETE = "master-data:DELETE"

    # Medical History Management
    MEDICAL_HISTORY_VIEW = "medical-history:VIEW"
    MEDICAL_HISTORY_CREATE = "medical-history:CREATE"
    MEDICAL_HISTORY_UPDATE = "medical-history:UPDATE"
    MEDICAL_HISTORY_DELETE = "medical-history:DELETE"

    # Medical Templates Management
    MEDICAL_TEMPLATES_VIEW = "medical-templates:VIEW"
    MEDICAL_TEMPLATES_CREATE = "medical-templates:CREATE"
    MEDICAL_TEMPLATES_UPDATE = "medical-templates:UPDATE"
    MEDICAL_TEMPLATES_DELETE = "medical-templates:DELETE"

    # Oral Examinations Management
    ORAL_EXAMINATIONS_VIEW = "oral-examinations:VIEW"
    ORAL_EXAMINATIONS_CREATE = "oral-examinations:CREATE"
    ORAL_EXAMINATIONS_UPDATE = "oral-examinations:UPDATE"
    ORAL_EXAMINATIONS_DELETE = "oral-examinations:DELETE"

    # Intraoral Examinations Management
    INTRAORAL_EXAMINATIONS_VIEW = "intraoral-examinations:VIEW"
    INTRAORAL_EXAMINATIONS_CREATE = "intraoral-examinations:CREATE"
    INTRAORAL_EXAMINATIONS_UPDATE = "intraoral-examinations:UPDATE"
    INTRAORAL_EXAMINATIONS_DELETE = "intraoral-examinations:DELETE"

    # Form Flows Management
    FORM_FLOWS_VIEW = "form-flows:VIEW"
    FORM_FLOWS_CREATE = "form-flows:CREATE"
    FORM_FLOWS_UPDATE = "form-flows:UPDATE"
    FORM_FLOWS_DELETE = "form-flows:DELETE"

    # Form Submissions Management
    FORM_SUBMISSIONS_VIEW = "form-submissions:VIEW"
    FORM_SUBMISSIONS_CREATE = "form-submissions:CREATE"
    FORM_SUBMISSIONS_UPDATE = "form-submissions:UPDATE"
    FORM_SUBMISSIONS_DELETE = "form-submissions:DELETE"

    # Reservations Management
    RESERVATIONS_VIEW = "reservations:VIEW"
    RESERVATIONS_CREATE = "reservations:CREATE"
    RESERVATIONS_UPDATE = "reservations:UPDATE"
    RESERVATIONS_DELETE = "reservations:DELETE"
