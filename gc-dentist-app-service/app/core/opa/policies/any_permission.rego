package noda.permission

default allow = false

# Allow rule for "ALL" condition
allow if {
    condition := input.requested_permission_key.condition

    condition == "ALL"
    all_permissions_granted
}

# Allow rule for "ANY" condition
allow if {
    condition := input.requested_permission_key.condition

    condition == "ANY"
    some_permission_granted
}

# Check if all requested permissions are granted
all_permissions_granted if {
    not exists_permission_not_granted
}

# Check if any permission is not granted
exists_permission_not_granted if {
    some p in input.requested_permission_key.permissions
    not p_in_all_permissions[p]
}

# Check if at least one permission is granted
some_permission_granted if {
    some p in input.requested_permission_key.permissions
    p_in_all_permissions[p]
}

# Helper function to check if a permission exists in all_permissions
p_in_all_permissions contains p if {
    some p in input.all_permissions.permissions
}
