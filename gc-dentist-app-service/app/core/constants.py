from enum import IntEnum


class TenantClinicStatus(IntEnum):
    INPROCESS = 20
    SUCCESS = 40
    FAILED = 90
    CANCELED = 99


TWILIO_SMS_CHANNEL = "sms"
TWILIO_STATUS_APPROVED = "approved"

FIELDS_ENCRYPTED = [
    "date_of_birth",
    "address_1",
    "address_2",
    "address_3",
    "phone",
    "email",
]

MAPPING_LANG_REGION = {
    "ja": "ja-JP",
    "en": "en-US",
}

X_TENANT_SLUG = "X-Tenant-Slug"
X_TENANT_UUID = "X-Tenant-UUID"

ENDPOINT_ACCOUNT_API = "http://localhost:8002"

DOCTOR_FIELDS_ENCRYPTED = [
    "date_of_birth",
    "phone",
    "email",
]
DOCTOR_FIELDS_HASHED = [
    "date_of_birth",
    "phone",
    "email",
]

PATIENT_FIELDS_ENCRYPTED = [
    "date_of_birth",
    "phone",
    "home_phone",
    "email",
]
PATIENT_FIELDS_HASHED = [
    "date_of_birth",
    "phone",
    "home_phone",
    "email",
]

PARAGRAPH_WITH_BREAK_OR_END_REGEX = r"<p[^>]*>(.*?)(<br>|</p>)"
REMOVE_HTML_TAGS_REGEX = r"<[^>]+>"

COUNTRY_CODE_JP = "+81"

IAPO_RESULT_SUCCESS = 0

DOCUMENT_MANAGEMENT_DEFAULT_VERSION = 1

KEY_LENGTH: int = 6
OTP_EXPIRY_MINUTES: int = 10
