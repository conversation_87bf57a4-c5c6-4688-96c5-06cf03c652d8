from contextlib import asynccontextmanager

from api.routes import router
from configuration.logger.config import log
from configuration.middleware.jwt_auth_middleware import JWTAuthMiddleware
from configuration.settings import Settings
from core.common.aes_gcm import AesGCMRotation
from db.db_connection import CentralDatabase
from fastapi import <PERSON><PERSON><PERSON>
from fastapi_versioning import VersionedFast<PERSON><PERSON>
from sqlalchemy import text
from starlette.middleware.authentication import AuthenticationMiddleware
from starlette_context.middleware import RawContextMiddleware


def create_app(skip_auth: bool = False) -> FastAPI:
    @asynccontextmanager
    async def lifespan(app: FastAPI):
        # startup logic
        await load_aes_key()
        session = CentralDatabase.get_sessionmaker()
        try:
            async with session() as conn:
                await conn.execute(text("SELECT 1"))
            print("✅ Database connected successfully")
        except Exception as e:
            print(f"❌ Database connection failed: {e}")
            raise e
        yield

    app = FastAPI(lifespan=lifespan)
    app.include_router(router)
    app = VersionedFastAPI(app)
    app.router.lifespan_context = lifespan

    register_middlewares(app)

    return app


def register_middlewares(app: FastAPI):
    env = Settings().ENVIRONMENT
    if env in ["production", "staging"]:
        from configuration.middleware.opentelemetry_log_middleware import (
            OpenTelemetryLogMiddleware,
        )

        app.add_middleware(OpenTelemetryLogMiddleware)
    app.add_middleware(
        AuthenticationMiddleware,
        backend=JWTAuthMiddleware(),
        on_error=JWTAuthMiddleware.auth_exception_handler,
    )
    app.add_middleware(RawContextMiddleware)


async def load_aes_key():
    env = Settings().ENVIRONMENT
    if env in ["production", "staging"]:
        (
            aws_secret_rotation_key_mapping,
            current_version_id,
        ) = await AesGCMRotation().get_key_from_aws_secrets_manager(
            Settings().AES_SECRET_ID_ROTATION
        )

        if aws_secret_rotation_key_mapping:
            Settings().AWS_SECRET_ROTATION_KEY_MAPPING = aws_secret_rotation_key_mapping

    if not Settings().AWS_SECRET_CURRENT_VERSION:
        log.error("❌ AES ENV secret get current version is None")
        return

    if not Settings().AWS_SECRET_ROTATION_KEY_MAPPING:
        log.error("❌ AES ENV secret key is None")
        return


app = create_app(skip_auth=True)
