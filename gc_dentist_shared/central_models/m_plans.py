from sqlalchemy import Column, Integer, Numeric, String, Text

from gc_dentist_shared.base.central_declarative_base import CentralBase
from gc_dentist_shared.base.datetime_mixin import DateTimeMixin


class MasterPlan(CentralBase, DateTimeMixin):
    """Master plans table for different subscription tiers in central database"""

    __tablename__ = "m_plans"

    id = Column(Integer, primary_key=True, autoincrement=True)
    code = Column(
        String(length=50),
        unique=True,
        nullable=False,
        comment="Plan code (e.g., basic, plus, premium)",
    )
    price = Column(Numeric(precision=10, scale=2), nullable=False, comment="Plan price")
    default_storage = Column(Integer, nullable=False)
    name = Column(Text, comment="Plan name in multiple languages")
