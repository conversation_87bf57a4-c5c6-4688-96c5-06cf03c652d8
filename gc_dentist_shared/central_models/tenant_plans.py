from sqlalchemy import Column, DateTime, <PERSON><PERSON><PERSON>, Integer, String
from sqlalchemy.dialects.postgresql import UUID

from gc_dentist_shared.base.central_declarative_base import CentralBase
from gc_dentist_shared.base.datetime_mixin import DateTimeMixin


class TenantPlan(CentralBase, DateTimeMixin):
    """Tenant plans table tracking which tenant is subscribed to which plan"""

    __tablename__ = "tenant_plans"

    id = Column(Integer, primary_key=True, autoincrement=True)
    tenant_uuid = Column(
        UUID(as_uuid=True), nullable=False, comment="Unique identifier of the tenant"
    )
    plan_id = Column(
        Integer,
        ForeignKey("m_plans.id", ondelete="RESTRICT"),
        nullable=False,
        comment="Reference to plan",
    )
    status = Column(
        String(length=20),
        nullable=False,
        default="active",
        comment="Plan status (active, inactive, suspended, etc.)",
    )
    start_at = Column(
        DateTime, nullable=False, comment="When the plan started for the tenant"
    )
    end_at = Column(
        DateTime,
        nullable=True,
        comment="When the plan ends for the tenant (null for unlimited)",
    )
