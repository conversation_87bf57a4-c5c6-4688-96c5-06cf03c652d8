from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Integer  # type: ignore
from sqlalchemy.dialects.postgresql import JSONB

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.base.tenant_declarative_base import TenantBase


class Roles(TenantBase, DateTimeMixin):
    """Roles table"""

    __tablename__ = "roles"

    id = Column(Integer, primary_key=True, autoincrement=True)
    name_json = Column(JSONB, comment="Master name for multiple languages")
    role_key = Column(
        Integer, default=None, comment="1: admin, 2: doctor, 3: patient", nullable=False
    )
    is_active = Column(Boolean, default=True, nullable=False)
    is_system = Column(Boolean, default=False, nullable=False)
    delete_flag = Column(Boolean, default=False)
