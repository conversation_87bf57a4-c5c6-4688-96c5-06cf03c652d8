from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer  # type: ignore

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.base.tenant_declarative_base import TenantBase


class Doctor<PERSON><PERSON>(TenantBase, DateTimeMixin):
    """Doctor Role table - maps doctors to their roles"""

    __tablename__ = "doctor_roles"

    id = Column(Integer, primary_key=True, autoincrement=True)
    doctor_user_id = Column(Integer, nullable=False)
    role_id = Column(Integer, ForeignKey("roles.id"), nullable=False)
    delete_flag = Column(<PERSON><PERSON>an, default=False, nullable=False)
