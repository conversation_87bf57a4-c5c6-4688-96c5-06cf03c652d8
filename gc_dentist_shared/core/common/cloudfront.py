from datetime import datetime, timedelta, timezone
from typing import Union
from urllib.parse import urlencode, urlparse

from botocore.signers import CloudFrontSigner
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.asymmetric import padding


class CloudFront:
    _instance = None

    def __init__(self, configuration):
        self.cloudfront_private_key = configuration.CLOUDFRONT_PRIVATE_KEY
        self.cloudfront_public_key_id = configuration.CLOUDFRONT_PUBLIC_KEY_ID
        self.cloudfront_url = configuration.CLOUDFRONT_URL
        self.cloudfront_expire_minutes = timedelta(
            minutes=configuration.CLOUDFRONT_SIGNED_URL_EXPIRE_MINUTES
        )

    @classmethod
    def get_instance(cls, configuration):
        if cls._instance is None:
            cls._instance = CloudFront(configuration=configuration)
        return cls._instance

    def _rsa_signer(self, message) -> bytes:
        return self.cloudfront_private_key.sign(
            message, padding.PKCS1v15(), hashes.SHA1()  # noqa: S303
        )

    def generate_signed_url(
        self,
        file_path: str,
        version: Union[str, int],
        return_full_url: bool = False,
    ) -> str:
        """
        Generate a time-limited CloudFront signed URL for private S3 files.
        Parameters:
        - file_path (str): S3 path (with or without leading '/').
        - version (Union[str, int]): file version.
        - return_full_url (bool): If True, returns full URL; else relative path.
        Returns:
        - str: Signed URL valid until `self.cloudfront_expire_minutes`.
        Example:
            url = generate_signed_url("clinic123/documents/report.pdf", version="v2")
            # Returns: "version=v2&Expires=....&Signature=......&Key-Pair-Id=....."
        """
        try:
            expires = datetime.now(timezone.utc) + self.cloudfront_expire_minutes
            path = file_path.lstrip("/")
            base_url = f"{self.cloudfront_url}/{path}?{urlencode({'version': version})}"

            cloudfront_signer = CloudFrontSigner(
                self.cloudfront_public_key_id, self._rsa_signer
            )
            signed_url = cloudfront_signer.generate_presigned_url(
                base_url, date_less_than=expires
            )

            if return_full_url:
                return signed_url

            parsed = urlparse(signed_url)
            relative_path = parsed.path.lstrip("/")
            if parsed.query:
                relative_path += f"?{parsed.query}"

            return relative_path
        except Exception as e:
            print(f"❌ Failed to generate signed URL: {e}")
            return file_path
