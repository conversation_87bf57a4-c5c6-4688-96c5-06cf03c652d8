import re

from sqlalchemy import Engine, event

# Pattern SQL dangerous
DANGEROUS_PATTERNS = [
    r"^\s*DELETE\s+FROM\s+\w+(\s*;)?\s*$",  # DELETE ALL DATA
    r"(?i)^\s*UPDATE\b\s+\w+\s+SET\b\s+.+(?!.*\bWHERE\b)",  # UPDATE without WHERE
    r"^\s*TRUNCATE\s+TABLE\s+\w+(\s*;)?\s*$",  # TRUNCATE
    r"^\s*DROP\s+TABLE\s+\w+(\s*;)?\s*$",  # DROP TABLE
    r"^\s*DROP\s+DATABASE\s+\w+(\s*;)?\s*$",  # DROP DATABASE
]

compiled_patterns = [re.compile(p, re.IGNORECASE) for p in DANGEROUS_PATTERNS]


@event.listens_for(Engine, "before_execute")
def prevent_dangerous_sql(conn, clauseelement, multiparams, params, execution_options):
    sql_text = str(clauseelement)

    for pattern in compiled_patterns:
        if pattern.match(sql_text):
            raise RuntimeError(f"⚠️  Dangerous SQL detected: {sql_text}")

    return clauseelement, multiparams, params
